/* Modern CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: "Inter", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  min-height: 100vh;
}

/* Modern Link Styles */
a {
  color: #4f46e5;
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: #3730a3;
  text-decoration: underline;
}

/* Modern Button Styles */
button,
.btn {
  display: inline-block;
  padding: 12px 24px;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  text-transform: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  margin: 8px 4px;
}

button:hover,
.btn:hover {
  background: #3730a3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
  color: white;
}

button:active,
.btn:active {
  transform: translateY(0);
}

.btn-secondary {
  background: #6b7280;
}

.btn-secondary:hover {
  background: #4b5563;
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
}

/* Modern Navbar Styles */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-left,
.nav-right {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  align-items: center;
}

.navbar li {
  position: relative;
  margin: 0 8px;
}

.navbar a {
  color: #374151;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.navbar a:hover {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  text-decoration: none;
}

/* Dropdown Menu Styles */
.navbar li ul {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  min-width: 200px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  padding: 8px 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  list-style: none;
  margin: 0;
}

.navbar li:hover ul {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.navbar li ul li {
  margin: 0;
  width: 100%;
}

.navbar li ul a {
  display: block;
  padding: 12px 16px;
  color: #374151;
  border-radius: 0;
}

.navbar li ul a:hover {
  background: #f3f4f6;
  color: #4f46e5;
}

/* Modern Content Container */
.content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  margin: 20px auto;
  max-width: 1200px;
  width: 90%;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Modern Typography */
.content p {
  margin: 16px 0;
  color: #374151;
  line-height: 1.7;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  text-align: center;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
}

.form-control:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-control::placeholder {
  color: #9ca3af;
}

/* Service Card Styles */
.services_list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.service-item {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.service-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  border-color: #4f46e5;
}

.service-item h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.service-item h3 a {
  color: #1f2937;
  text-decoration: none;
}

.service-item h3 a:hover {
  color: #4f46e5;
}

.service-price {
  font-size: 1.125rem;
  font-weight: 700;
  color: #059669;
  margin: 0.5rem 0;
}

.service-field {
  display: inline-block;
  background: #ede9fe;
  color: #7c3aed;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0.5rem 0;
}

.service-description {
  color: #6b7280;
  margin: 1rem 0;
  line-height: 1.6;
}

.service-company {
  color: #9ca3af;
  font-size: 0.875rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
}

.service-stats {
  margin-top: 0.5rem;
}

.request-count {
  background: #10b981;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Modern Form Section Styles */
.registration-container {
  max-width: 600px;
  margin: 0 auto;
}

.modern-form {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.form-section:last-of-type {
  border-bottom: none;
  margin-bottom: 1rem;
}

.section-title {
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #4f46e5;
  display: inline-block;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f3f4f6;
}

.error-message {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  border-left: 4px solid #dc2626;
  font-weight: 500;
}

.login-container .error-message {
  margin-top: 0.5rem;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.help-text {
  color: #6b7280;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

/* Service Detail Styles */
.service-detail-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.service-detail-header {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.service-detail-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.service-detail-price {
  font-size: 1.5rem;
  font-weight: 600;
  opacity: 0.9;
}

.service-detail-meta {
  padding: 1.5rem 2rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-detail-category {
  background: #4f46e5;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.service-detail-company {
  color: #6b7280;
  font-size: 0.875rem;
}

.service-detail-company a {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 500;
}

.service-detail-company a:hover {
  text-decoration: underline;
}

.service-detail-description {
  padding: 2rem;
}

.service-detail-description h3 {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.service-detail-description p {
  color: #4b5563;
  line-height: 1.6;
  font-size: 1rem;
}

.service-detail-actions {
  padding: 2rem;
  background: #f8fafc;
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Service Request Styles */
.service-request-container {
  max-width: 600px;
  margin: 0 auto;
}

.service-request-info {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  border: 1px solid #e5e7eb;
}

.service-request-info h2 {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.service-request-price {
  color: #4f46e5;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.service-request-description {
  color: #6b7280;
  line-height: 1.6;
}

/* No Services Message */
.no-services-message {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.no-services-message h2 {
  color: #6b7280;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.no-services-message p {
  color: #9ca3af;
  margin-bottom: 2rem;
}

/* Home Page Styles */
.home-hero {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4rem 0;
  min-height: 60vh;
}

.hero-content {
  flex: 1;
  max-width: 600px;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  color: #1f2937;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: #6b7280;
  margin-bottom: 1rem;
  font-weight: 500;
}

.hero-description {
  font-size: 1.125rem;
  color: #4b5563;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-image {
  flex: 1;
  text-align: center;
  max-width: 400px;
}

.home_logo {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

.home-features {
  background: white;
  padding: 4rem 0;
  margin-top: 2rem;
}

.features-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 3rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-item {
  text-align: center;
  padding: 2rem;
  border-radius: 12px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-item h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.feature-item p {
  color: #6b7280;
  line-height: 1.6;
}

/* Registration Choice Styles */
.registration-choice-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.registration-subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 3rem;
}

.registration-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.registration-option {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 2rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  display: block;
}

.registration-option:hover {
  border-color: #4f46e5;
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(79, 70, 229, 0.15);
  text-decoration: none;
  color: inherit;
}

.option-icon {
  margin-bottom: 1.5rem;
}

.option-icon img {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.registration-option h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.registration-option p {
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.option-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.option-features span {
  color: #10b981;
  font-size: 0.875rem;
  font-weight: 500;
}

.registration-footer {
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.registration-footer p {
  color: #6b7280;
}

.registration-footer a {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 500;
}

.registration-footer a:hover {
  text-decoration: underline;
}

/* Login Page Styles */
.login-container {
  max-width: 450px;
  margin: 2rem auto;
  text-align: center;
  padding: 0 1rem;
}

.login-subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 2rem;
}

.login-container .modern-form {
  text-align: left;
  margin-top: 2rem;
}

.login-container .form-group {
  margin-bottom: 1.5rem;
}

.login-container .form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.login-container .form-control {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.login-container .form-control:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.login-container .form-control::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.login-container .btn-primary {
  width: 100%;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  margin-top: 1rem;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(79, 70, 229, 0.2);
}

.login-container .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(79, 70, 229, 0.3);
}

.login-container .btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.login-footer {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.login-footer p {
  color: #6b7280;
  font-size: 0.875rem;
}

.login-footer a {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.login-footer a:hover {
  color: #3730a3;
  text-decoration: underline;
}

/* Profile Page Styles */
.profile-container {
  max-width: 800px;
  margin: 0 auto;
}

.profile-header {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e5e7eb;
}

.profile-title {
  color: #1f2937;
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
}

.profile-type {
  background: #4f46e5;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.profile-info {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  border: 1px solid #e5e7eb;
}

.profile-info h3 {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item label {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

.info-item span {
  color: #1f2937;
  font-weight: 500;
}

.profile-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.profile-section h3 {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.request-details {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-item label {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-item span {
  color: #1f2937;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .content {
    width: 95%;
    padding: 20px;
  }

  .modern-form {
    padding: 1.5rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .services_list {
    grid-template-columns: 1fr;
  }

  .navbar {
    padding: 1rem;
  }

  .navbar li ul {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    background: transparent;
    padding: 0;
  }

  .service-detail-container {
    margin: 1rem;
  }

  .service-detail-header {
    padding: 1.5rem;
  }

  .service-detail-title {
    font-size: 1.5rem;
  }

  .service-detail-meta {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .service-detail-actions {
    flex-direction: column;
  }

  .home-hero {
    flex-direction: column;
    text-align: center;
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-actions {
    justify-content: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .registration-options {
    grid-template-columns: 1fr;
  }

  .registration-option {
    padding: 1.5rem;
  }

  .login-container,
  .registration-choice-container {
    padding: 0 1rem;
  }
}

.choice_text {
  text-align: center;
  font-size: 1.5em;
  color: #333;
  font-weight: bold;
}

/* Container with your original div structure */
.choices-wrapper {
  position: relative;
  margin-top: 70px;
  min-height: 200px;
}

.choice {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.choice img {
  width: 120px;
  height: 120px;
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.choice:hover img {
  transform: scale(1.05);
}

.choice a {
  text-decoration: none;
}

.label_images {
  margin-top: 10px;
  font-weight: bold;
  color: #333;
}

/* Full screen - side by side layout */
.choice:first-child {
  margin-left: 50px;
}

.choice:last-child {
  float: right;
  margin-right: 50px;
}

/* Responsive behavior for smaller screens */
@media (max-width: 768px) {
  .choices-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    margin-top: 40px;
  }

  .choice:first-child {
    margin-left: 0;
  }

  .choice:last-child {
    float: none;
    margin-right: 0;
  }

  .choice {
    width: 100%;
    max-width: 200px;
  }
}

.like_button {
  background-color: white;
  border: 2px solid transparent;
  border-radius: 5px;
  padding: 10px;
  transition: background 0.7s;
  color: #ec2f00;
  text-decoration: none;
  text-transform: uppercase;
  cursor: pointer;
}

.like_button:hover {
  background: #ec4138;
  border: 2px solid #f05749;
  border-right: 2px solid #e02a21;
  border-bottom: 2px solid #e02a21;
  color: #f9f8fd;
}

.title {
  text-align: center;
  font-family: "Times New Roman", Times, serif;
  font-size: 60px;
  font-weight: bold;
  text-decoration: 2px underline;
  text-decoration-color: rgba(255, 68, 0, 0.37);
}
/* ======================================== Home Page ============================================== */

.home_logo {
  display: block;
  margin: 0 auto;
  margin-bottom: 70px;
}

.site_title {
  text-align: center;
  font-size: 70px;
}

/* ============================================================================================== */

/* ======================================== Navbar ============================================== */

.navbar {
  position: sticky;
  top: 15px;
  box-shadow: inset 1px -1px 10px rgb(116, 116, 116);
  border: 2px solid white;
  border-radius: 30px;
  animation: slide-in 1s ease-out;
  background-color: #e7e7e7;
  width: 95%;
  margin: 0 auto;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
}

.navbar ul {
  position: relative;
  display: flex;
  margin: 0;
  padding: 0 30px;
  list-style-type: none;
}

.nav-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.nav-right {
  display: flex;
  align-items: center;
  margin-right: 30px;
}

/* Override the existing margin for the last child in nav-right */
.nav-right li:not(:last-child) {
  margin-right: 40px;
}

/* Keep the existing hover and dropdown styles */
.navbar ul li {
  border: 2px solid transparent;
  border-radius: 5px;
  padding: 10px;
  transition: background 0.2s;
  white-space: nowrap;
}

/* Ensure dropdowns work properly with new layout */
.navbar ul li ul {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  display: block;
  margin: 12px -12px;
  padding: 0;
  background: #ffa91b;
  border: 2px solid #f7c833;
  border-right: 2px solid #f89329;
  border-bottom: 2px solid #f89329;
  border-radius: 5px;
  transition: opacity 0.2s, visibility 0.2s;
  left: 0; /* Ensure dropdown aligns with parent */
}

/* Ensure the Services dropdown remains visible */
.navbar .nav-left > li:hover ul {
  visibility: visible;
  opacity: 1;
  z-index: 1000;
}

.navbar ul li ul li {
  margin: -2px 0 0 -2px;
  width: calc(100% - 20px);
  line-height: 1.7;
}

.navbar ul li:hover {
  background: #ec4138;
  border: 2px solid #f05749;
  border-right: 2px solid #e02a21;
  border-bottom: 2px solid #e02a21;
}
.navbar ul li:hover a {
  color: #f9f8fd;
}
.navbar ul li:hover ul {
  visibility: visible;
  opacity: 1;
  box-shadow: 0px 3px 5px 2px #ebecf1;
}
.navbar ul li:hover ul li a {
  color: #f9f8fd;
}

.last_navbar {
  right: 0;
  position: absolute;
  margin-right: 20px;
}

/* ============================================================================================== */

/* ======================================== Service Info ======================================== */

.service_info_header {
  display: block ruby;
}
.service_info_header h5 {
  float: right;
}

/* ============================================================================================== */

/* ======================================== Service List ======================================== */

.services_list {
  padding: 0;
  margin-bottom: 20px;
}

pre {
  display: none;
}

.service_list_info {
  padding: 0 30px;
}
.service_list_info li {
  list-style-type: none;
}
.service_list_info li a:hover > pre {
  display: block;
}

.line {
  width: 100%;
  margin: 0 auto;
  height: 2px;
  background-color: #00000044;
  border-radius: 20px;
  margin-bottom: 10px;
  margin-top: 35px;
}

.create_service {
  background: antiquewhite;
  padding: 10px 20px;
  border-radius: 17px;
  display: table;
  margin: 0 auto;
  border: 2px solid #474747;
  margin-bottom: 60px;
  text-decoration: none;
}

.create_service:hover {
  background-color: #b8b8b8;
  box-shadow: 2px 3px 6px 1px #21212173;
  transition: 0.5s;
}
.create_service:not(hover) {
  transition: 0.6s;
}

.list_services_profile {
  font-size: x-large;
}

/* ============================================================================================== */

/* ===================================== Choose user type ======================================= */

.choice_text {
  text-shadow: 0px 0px 1px #e02a21;
  color: black;
  text-align: center;
  font-size: 40px;
  font-weight: initial;
}

.choice {
  display: inline-grid;
}

.img {
  border: 1px solid #b8b8b8;
  border-radius: 14px;
  display: inline-block;
  padding: 20px;
  box-shadow: 0px 0px 5px 5px rgb(50, 50, 50);
  cursor: pointer;
}
.img:hover {
  box-shadow: 0px 0px 9px 5px rgb(27, 27, 27);
}
.img:active {
  background-color: #474747;
  transition: 0.1s;
  box-shadow: 0px 0px 9px 5px black;
}

.label_images {
  display: block;
}

form label {
  display: none;
}

form {
  margin: 0 auto;
  display: grid;
  width: 65%;
}

form input {
  padding: 10px 3px;
  font-size: 20px;
  display: block;
  border: none;
  border-bottom: 3px solid black;
  background-color: inherit;
  margin-top: 30px;
}

form input:hover,
input:focus {
  transition: 0.5s;
  border-bottom: 3px solid orangered;
}

form select {
  display: block;
  font-size: 20px;
  padding: 6px 3px;
  background: inherit;
  border: none;
  border-bottom: 3px solid black;
  margin-top: 30px;
}

form select:focus,
select:hover {
  transition: 0.5s;
  border-bottom: 3px solid orangered;
}

.error_message {
  color: #ff4040;
  text-shadow: 0px 0px 10px #800;
}

form textarea {
  display: block;
  border: none;
  border-bottom: 3px solid black;
  background: inherit;
  font-size: inherit;
  font-weight: inherit;
  font-family: inherit;
  margin-top: 30px;
}

form textarea:hover,
textarea:focus {
  transition: 0.5s;
  border-bottom: 3px solid orangered;
}

span {
  background-color: #3834343d;
  border-radius: 30px;
  box-shadow: 0px 0px 23px 1px #2423213d;
  padding: 5px 10px;
  margin: 5px 0px;
}

/* ============================================================================================== */

/* Service Request Cards Styling */
.requests-container {
  display: grid;
  gap: 24px;
  margin-top: 24px;
}

.request-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.request-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.request-service-name {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.request-status {
  background: #fbbf24;
  color: #92400e;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.request-details {
  margin-bottom: 24px;
}

.request-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.request-info-item {
  display: flex;
  flex-direction: column;
}

.request-info-item label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.request-info-item span {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

.request-cost {
  color: #059669 !important;
  font-weight: 700 !important;
  font-size: 16px !important;
}

.request-address {
  background: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.request-address label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  display: block;
}

.request-address p {
  margin: 0;
  color: #1f2937;
  font-size: 14px;
  line-height: 1.5;
}

.request-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.request-actions .btn {
  margin: 0;
  flex: 1;
  min-width: 140px;
  text-align: center;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  margin-top: 24px;
}

.empty-state h3 {
  color: #1f2937;
  font-size: 24px;
  margin-bottom: 12px;
}

.empty-state p {
  color: #6b7280;
  font-size: 16px;
  margin-bottom: 24px;
}

/* Responsive design for request cards */
@media (max-width: 768px) {
  .request-info-grid {
    grid-template-columns: 1fr;
  }

  .request-actions {
    flex-direction: column;
  }

  .request-actions .btn {
    flex: none;
    width: 100%;
  }
}
