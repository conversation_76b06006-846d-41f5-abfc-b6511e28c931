from django import forms

from users.models import Company
from .models import Service, RequestedService


class CreateNewService(forms.Form):
    name = forms.CharField(max_length=40)
    description = forms.CharField(widget=forms.Textarea, label='Description')
    price_hour = forms.DecimalField(
        decimal_places=2, max_digits=5, min_value=0.00)
    field = forms.ChoiceField(required=True)

    def __init__(self, *args, choices='', ** kwargs):
        super(CreateNewService, self).__init__(*args, **kwargs)
        # adding choices to fields
        if choices:
            self.fields['field'].choices = choices

        # adding CSS classes and placeholders to form fields
        self.fields['name'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter service name (e.g., Home Electrical Repair)',
            'autocomplete': 'off'
        })
        self.fields['description'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Describe your service in detail...',
            'rows': '4'
        })
        self.fields['price_hour'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '25.00',
            'step': '0.01',
            'min': '0'
        })
        self.fields['field'].widget.attrs.update({
            'class': 'form-control'
        })


class RequestServiceForm(forms.Form):
    address = forms.CharField(widget=forms.Textarea, label='Service Address')
    hours = forms.DecimalField(
        decimal_places=2, max_digits=5, min_value=0.00, label='Hours Needed')

    def __init__(self, *args, **kwargs):
        super(RequestServiceForm, self).__init__(*args, **kwargs)

        # Adding CSS classes and placeholders to form fields
        self.fields['address'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter the full address where the service is needed...',
            'rows': '3'
        })
        self.fields['hours'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter estimated hours (e.g., 2.5)',
            'step': '0.1',
            'min': '0.1'
        })