{% extends 'main/base.html' %}

{% block title %}
Create New Service
{% endblock %}

{% block content %}
<div class="content">
  <h1 class="title">Create New Service</h1>

  <div class="registration-container">
    <form method="POST" class="modern-form">
      {% csrf_token %}

      <div class="form-section">
        <h3 class="section-title">Service Information</h3>
        <div class="form-group">
          <label for="id_name" class="form-label">Service Name</label>
          <input
            type="text"
            name="name"
            id="id_name"
            class="form-control"
            placeholder="Enter service name (e.g., Home Electrical Repair)"
            required
          />
          {% if form.name.errors %}
          <div class="error-message">{{ form.name.errors }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="id_field" class="form-label">Service Category</label>
          <select name="field" id="id_field" class="form-control" required>
            <option value="">Select service category</option>
            <option value="Air Conditioner">Air Conditioner</option>
            <option value="All in One">All in One</option>
            <option value="Carpentry">Carpentry</option>
            <option value="Electricity">Electricity</option>
            <option value="Gardening">Gardening</option>
            <option value="Home Machines">Home Machines</option>
            <option value="Housekeeping">Housekeeping</option>
            <option value="Interior Design">Interior Design</option>
            <option value="Locks">Locks</option>
            <option value="Painting">Painting</option>
            <option value="Plumbing">Plumbing</option>
            <option value="Water Heaters">Water Heaters</option>
          </select>
          {% if form.field.errors %}
          <div class="error-message">{{ form.field.errors }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-section">
        <h3 class="section-title">Service Details</h3>
        <div class="form-group">
          <label for="id_description" class="form-label">Description</label>
          <textarea
            name="description"
            id="id_description"
            class="form-control"
            rows="4"
            placeholder="Describe your service in detail..."
            required
          ></textarea>
          {% if form.description.errors %}
          <div class="error-message">{{ form.description.errors }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="id_price_hour" class="form-label"
            >Price per Hour (€)</label
          >
          <input
            type="number"
            name="price_hour"
            id="id_price_hour"
            class="form-control"
            placeholder="25.00"
            step="0.01"
            min="0"
            required
          />
          {% if form.price_hour.errors %}
          <div class="error-message">{{ form.price_hour.errors }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-actions">
        <button type="submit" class="btn btn-primary">Create Service</button>
        <a href="/services/" class="btn btn-secondary">Cancel</a>
      </div>
    </form>
  </div>
</div>
{% endblock %}
