{% extends 'main/base.html' %} {% block title %} Request Service {% endblock %}
{% block content %}
<div class="content">
  <h1 class="title">Request Service</h1>

  <div class="service-request-container">
    <div class="service-request-info">
      <h2>{{ service.name }}</h2>
      <p class="service-request-price">{{ service.price_hour }}€/hour</p>
      <p class="service-request-description">{{ service.description }}</p>
    </div>

    <form method="POST" class="modern-form">
      {% csrf_token %}

      <div class="form-section">
        <h3 class="section-title">Request Details</h3>

        <div class="form-group">
          <label for="{{ form.address.id_for_label }}" class="form-label">
            Service Address
            <span class="required-indicator">*</span>
          </label>
          {{ form.address }}
          <small class="help-text"
            >Please provide the complete address where the service is
            needed</small
          >
          {% if form.address.errors %}
          <div class="error-message">{{ form.address.errors }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="{{ form.hours.id_for_label }}" class="form-label">
            Estimated Hours
            <span class="required-indicator">*</span>
          </label>
          {{ form.hours }}
          <small class="help-text"
            >Estimate how many hours you think this service will take</small
          >
          {% if form.hours.errors %}
          <div class="error-message">{{ form.hours.errors }}</div>
          {% endif %}
        </div>

        <div class="cost-calculation">
          <div class="cost-breakdown">
            <span class="cost-label">Estimated Cost:</span>
            <span class="cost-value" id="estimated-cost"
              >€{{ service.price_hour }}</span
            >
          </div>
          <small class="cost-note"
            >Final cost will be calculated based on actual hours worked</small
          >
        </div>
      </div>

      <div class="form-actions">
        <button type="submit" class="btn btn-primary">Submit Request</button>
        <a href="/services/{{ service.id }}/" class="btn btn-secondary"
          >Cancel</a
        >
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
      const hoursInput = document.querySelector('input[name="hours"]');
      const estimatedCost = document.getElementById('estimated-cost');
      const pricePerHour = {{ service.price_hour }};

      function updateCost() {
          const hours = parseFloat(hoursInput.value) || 0;
          const totalCost = (hours * pricePerHour).toFixed(2);
          estimatedCost.textContent = '€' + totalCost;
      }

      if (hoursInput) {
          hoursInput.addEventListener('input', updateCost);
          hoursInput.addEventListener('change', updateCost);
      }
  });
</script>
{% endblock %}
