{% extends 'main/base.html' %}

{% block title %}
Service Requests
{% endblock %}

{% block content %}
<div class="content">
  <h1 class="title">Service Requests</h1>

  {% if requested_services %}
  <div class="requests-container">
    {% for request in requested_services %}
    <div class="request-card">
      <div class="request-header">
        <h3 class="request-service-name">{{ request.service.name }}</h3>
        <span class="request-status">Pending</span>
      </div>

      <div class="request-details">
        <div class="request-info-grid">
          <div class="request-info-item">
            <label>Customer</label>
            <span>{{ request.customer.user.username }}</span>
          </div>
          <div class="request-info-item">
            <label>Hours Needed</label>
            <span>{{ request.hours }} hours</span>
          </div>
          <div class="request-info-item">
            <label>Total Cost</label>
            <span class="request-cost">€{{ request.total_cost }}</span>
          </div>
          <div class="request-info-item">
            <label>Date Requested</label>
            <span>{{ request.date_requested|date:"F j, Y g:i A" }}</span>
          </div>
        </div>

        <div class="request-address">
          <label>Service Address</label>
          <p>{{ request.address }}</p>
        </div>
      </div>

      <div class="request-actions">
        <button class="btn btn-primary">Accept Request</button>
        <button class="btn btn-secondary">Contact Customer</button>
      </div>
    </div>
    {% endfor %}
  </div>
  {% else %}
  <div class="empty-state">
    <h3>No Service Requests Yet</h3>
    <p>When customers request your services, they will appear here.</p>
    <a href="/services/" class="btn btn-primary">View All Services</a>
  </div>
  {% endif %}
</div>
{% endblock %}
